# PID参数调试备选方案

## 方案2：仅增加Kp（如果方案1不理想时使用）

```c
PidParams_t pid_params_left = {
    .kp = 5.5f,      // 大幅增加P增益
    .ki = 0.0f,      // 保持为0
    .kd = 0.0f,      // 保持为0
    .out_min = -999.0f,
    .out_max = 999.0f,
};
```

## 调试步骤

1. 先尝试方案1（Kp=4.0, Ki=0.2）
2. 如果出现超调或振荡，减小Ki到0.1
3. 如果仍有稳态误差，适当增加Ki到0.3-0.5
4. 如果方案1完全不适用，回退到方案2

## 预期效果

- **方案1**：稳态误差接近0，可能有轻微超调
- **方案2**：稳态误差减小但可能仍存在

## 理论依据

积分项Ki的作用：
- 累积历史误差
- 当存在稳态误差时，积分项会持续增大
- 提供额外的控制输出来消除稳态误差
- 这是消除稳态误差的根本解决方案
