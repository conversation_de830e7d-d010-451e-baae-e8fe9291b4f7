#ifndef __MYDEFINE_H__
#define __MYDEFINE_H__

/* ========== HAL 库头文件 ========== */
#include "main.h"
#include "gpio.h"
#include "dma.h"
#include "tim.h"
#include "usart.h"
#include "i2c.h"
#include "stdio.h"
/* ========== C 语言头文件 ========== */
#include <stdio.h>
#include <string.h>
#include <stdarg.h>
#include <math.h>
#include <stdint.h>
#include <stdbool.h>

/* ========== 核心调度器头文件 ========== */
#include "Scheduler.h"

/* ========== 组件库头文件 ========== */
#include "ebtn.h"
#include "hardware_iic.h"
#include "pid.h"
#include "ringbuffer.h"

/* ========== 驱动库头文件 ========== */
#include "motor_driver.h"
#include "encoder_driver.h"
#include "ebtn_driver.h"
#include "led_driver.h"
#include "uart_driver.h"
#include "hwt101_driver.h"

/* ========== 应用层头文件 ========== */
#include "motor_app.h"
#include "encoder_app.h" 
#include "key_app.h"
#include "led_app.h"
#include "gray_app.h"
#include "pid_app.h"
#include "uart_app.h"

#include "oled_app.h"
#include "oled.h"
#include "oled_font.c"
#include "StepMotor_app.h"
#include "Emm_V5.h"
#include "gd25qxx.h"

/* ========== 编码器相关宏定义 ========== */
#define MOTOR_RX_BUFFER_SIZE 10      // 编码器接收缓冲区大小
#define MOTOR_START_BYTE_01 0x01     // X轴电机起始字节
#define MOTOR_START_BYTE_02 0x02     // Y轴电机起始字节
#define MOTOR_END_BYTE 0x6B          // 数据帧结束字节
#define MOTOR_READY_FLAG_1 0xFD      // 到位标识字节1
#define MOTOR_READY_FLAG_2 0x9F      // 到位标识字节2

/* ========== 全局用户变量 ========== */
extern Motor_t right_motor;
extern Motor_t left_motor;
extern HWT101_t hwt101;
extern uint8_t led_buf[4];

/* ========== 编码器全局变量声明 ========== */
extern uint8_t motor_x_rx_byte;      // X轴编码器接收字节缓冲
extern uint8_t motor_y_rx_byte;      // Y轴编码器接收字节缓冲
extern uint8_t motor_x_ready;        // X轴电机到位标志
extern uint8_t motor_y_ready;        // Y轴电机到位标志
extern uint8_t stop_flag_car;        // 系统状态标志 (0:正常, 1:到位, 2:错误)

/* ========== 脉冲计数全局变量声明 ========== */
extern int32_t motor_x_pulse_count;    // X轴当前脉冲计数
extern int32_t motor_y_pulse_count;    // Y轴当前脉冲计数
extern int32_t motor_x_target_pulses;  // X轴目标脉冲数
extern int32_t motor_y_target_pulses;  // Y轴目标脉冲数

#endif


