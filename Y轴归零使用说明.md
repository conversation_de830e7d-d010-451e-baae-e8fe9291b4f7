# Y轴自动归零功能使用说明

## 功能概述
已为您的Y轴电机（串口4，地址0x01）添加了自动归零功能，实现：
1. **上电自动回零** - 每次系统启动时Y轴自动回到零点
2. **手动设置零点** - 可随时重新设置当前位置为零点

## 实现原理
使用您现有的Emm_V5步进电机库中的归零函数：
- `Emm_V5_Origin_Modify_Params()` - 设置归零参数
- `Emm_V5_Origin_Set_O()` - 设置当前位置为零点  
- `Emm_V5_Origin_Trigger_Return()` - 触发回零动作

## 修改内容

### 1. StepMotor_Init() 函数增强
在 `StepMotor_app.c` 的初始化函数中添加了Y轴自动归零设置：

```c
/* ========== Y轴自动归零设置 ========== */
// 设置Y轴上电自动回零参数
// 参数说明: 存储到Flash, 单圈就近回零, CW方向, 500RPM速度, 10秒超时, 其他参数使用默认值, 上电自动触发
Emm_V5_Origin_Modify_Params(&MOTOR_Y_UART, MOTOR_Y_ADDR, true, 0, 0, 500, 10000, 100, 500, 100, true);

// 设置当前位置为零点
Emm_V5_Origin_Set_O(&MOTOR_Y_UART, MOTOR_Y_ADDR, true);

// 立即触发Y轴回零
Emm_V5_Origin_Trigger_Return(&MOTOR_Y_UART, MOTOR_Y_ADDR, 0, false);
```

### 2. 新增手动设置零点函数
添加了 `StepMotor_Y_Set_Origin()` 函数，可随时手动设置零点：

```c
/**
 * @brief Y轴手动设置零点并回零
 */
void StepMotor_Y_Set_Origin(void)
{
    // 设置当前位置为零点
    Emm_V5_Origin_Set_O(&MOTOR_Y_UART, MOTOR_Y_ADDR, true);
    
    // 触发回零
    Emm_V5_Origin_Trigger_Return(&MOTOR_Y_UART, MOTOR_Y_ADDR, 0, false);
    
    my_printf(&huart1, "Y-Axis Manual Origin Set\r\n");
}
```

## 使用方法

### 自动归零（已自动生效）
- 每次系统上电时，Y轴会自动回到零点
- 无需任何额外操作

### 手动设置零点
当需要重新设置零点时，调用：
```c
StepMotor_Y_Set_Origin();
```

## 参数说明
当前设置的归零参数：
- **回零模式**: 0 (单圈就近回零)
- **回零方向**: 0 (CW顺时针方向)  
- **回零速度**: 500 RPM
- **超时时间**: 10秒
- **上电自动触发**: true (启用)
- **参数存储**: true (保存到Flash)

## 注意事项
1. 参数已保存到Flash，断电后不会丢失
2. 如需修改归零参数，可调用 `Emm_V5_Origin_Modify_Params()` 函数
3. 归零过程中请确保Y轴运动路径无障碍物
4. 可通过串口1查看归零状态信息

## 测试验证
1. 重新编译并下载程序
2. 观察串口1输出，应看到 "Y-Axis Auto Origin Set OK" 信息
3. 手动移动Y轴到任意位置，然后重启系统
4. 观察Y轴是否自动回到零点位置

## 故障排除
如果归零功能异常：
1. 检查串口4连接是否正常
2. 确认Y轴电机地址为0x01
3. 检查电机供电是否正常
4. 观察串口输出的错误信息
