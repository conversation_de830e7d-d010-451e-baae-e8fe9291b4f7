/**
 * @file path_planner.h
 * @brief 智能路径规划系统头文件
 * <AUTHOR> Code
 */

#ifndef __PATH_PLANNER_H__
#define __PATH_PLANNER_H__

#include "MyDefine.h"

/* 路径规划配置 */
#define MAX_PATH_POINTS         10      // 最大路径点数量
#define LARGE_MOVE_THRESHOLD    50      // 大幅移动阈值(像素)
#define MEDIUM_MOVE_THRESHOLD   20      // 中等移动阈值(像素)
#define PATH_SEGMENT_SIZE       30      // 路径分段大小(像素)

/* 路径策略枚举 */
typedef enum {
    PATH_DIRECT,            // 直接路径
    PATH_STAGED,            // 分段路径
    PATH_L_SHAPED,          // L型路径(先X后Y)
    PATH_REVERSE_L,         // 反L型路径(先Y后X)
    PATH_CURVED            // 曲线路径
} PathStrategy_t;

/* 路径点结构体 */
typedef struct {
    int16_t x;              // X坐标
    int16_t y;              // Y坐标
    uint8_t speed_percent;  // 该点的速度百分比
    uint16_t hold_time_ms;  // 在该点停留时间(ms)
} PathPoint_t;

/* 路径规划器结构体 */
typedef struct {
    PathPoint_t points[MAX_PATH_POINTS];    // 路径点数组
    uint8_t point_count;                    // 当前路径点数量
    uint8_t current_index;                  // 当前正在执行的路径点索引
    PathStrategy_t strategy;                // 当前使用的路径策略
    bool is_active;                         // 路径规划是否激活
    bool path_complete;                     // 路径是否执行完成
    uint32_t last_update_time;              // 上次更新时间
} PathPlanner_t;

/* 函数声明 */
void PathPlanner_Init(void);
bool PathPlanner_AnalyzePath(int16_t current_x, int16_t current_y, 
                             int16_t target_x, int16_t target_y);
bool PathPlanner_GetNextTarget(int16_t *next_x, int16_t *next_y);
bool PathPlanner_AdvanceToNext(void);
bool PathPlanner_IsComplete(void);
void PathPlanner_Reset(void);
PathStrategy_t PathPlanner_GetStrategy(void);
uint8_t PathPlanner_GetProgress(void);
PathPoint_t* PathPlanner_GetCurrentPoint(void);

#endif /* __PATH_PLANNER_H__ */